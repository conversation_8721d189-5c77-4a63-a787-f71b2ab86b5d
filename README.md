# 征信报告自动化处理工具

## 项目简介

这是一个基于AI的征信报告自动化处理工具，支持个人征信报告和企业征信报告的PDF文件处理，自动提取关键信息并存储到飞书多维表格中。

## 主要功能

- 📄 支持个人征信和企业征信两种报告类型
- 🚀 批量处理PDF文件（最多10份）
- 🤖 使用AI自动提取结构化数据
- 📊 直接存储到飞书多维表格
- ⚡ 实时处理状态展示
- 🔄 失败重试机制
- 🎯 多AI服务商支持

## 技术栈

### 后端
- **框架**: Python + FastAPI
- **AI服务**: Google Gemini 1.5 Pro (主要) + Moonshot Kimi + OpenAI (备选)
- **PDF处理**: PyPDF2 + pdfplumber
- **API集成**: 飞书多维表格API

### 前端
- **技术**: 原生HTML/CSS/JavaScript
- **样式**: Tailwind CSS
- **特性**: 拖拽上传、实时状态更新

## 快速开始

### 环境要求
- Python 3.8+
- Node.js 16+ (可选，用于前端开发)

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd credit-report-processor
```

2. **后端设置**
```bash
cd backend

# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# macOS/Linux
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件，填入真实的API密钥和配置
```

3. **启动后端服务**
```bash
uvicorn app.main:app --reload --port 8000
```

4. **访问前端**
```bash
# 直接打开浏览器访问
frontend/index.html
```

### 配置说明

#### 必需配置
- `GEMINI_API_KEY`: Google Gemini API密钥
- `FEISHU_APP_ID`: 飞书应用ID
- `FEISHU_APP_SECRET`: 飞书应用密钥
- `FEISHU_APP_TOKEN`: 飞书多维表格Token
- `FEISHU_TABLE_ID`: 飞书表格ID

#### 可选配置
- `KIMI_API_KEY`: Moonshot Kimi API密钥（备选AI服务）
- `OPENAI_API_KEY`: OpenAI API密钥（备选AI服务）

## 使用方法

1. 打开Web界面
2. 选择报告类型（个人征信/企业征信）
3. 上传PDF文件（支持多文件选择）
4. 点击"上传并处理"按钮
5. 查看实时处理状态
6. 处理完成后数据自动存储到飞书表格

## 项目结构

```
credit-report-processor/
├── backend/                    # 后端代码
│   ├── app/
│   │   ├── main.py            # FastAPI应用入口
│   │   ├── api/               # API路由
│   │   ├── services/          # 业务服务
│   │   ├── config/            # 配置管理
│   │   ├── models/            # 数据模型
│   │   └── utils/             # 工具函数
│   ├── requirements.txt       # Python依赖
│   └── .env.example          # 环境变量示例
├── frontend/                  # 前端代码
│   ├── index.html            # 主页面
│   └── assets/               # 静态资源
├── docs/                     # 文档
├── 需求/                     # 需求文档
├── 开发计划.md               # 开发计划
├── TODO清单.md              # 任务清单
└── README.md                # 项目说明
```

## 开发进度

- [x] 项目初始化
- [ ] 后端基础框架
- [ ] PDF处理服务
- [ ] AI服务集成
- [ ] 飞书API集成
- [ ] 前端界面开发
- [ ] 测试和调试

详细进度请查看 [TODO清单.md](./TODO清单.md)

## API文档

启动后端服务后，访问以下地址查看API文档：
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交 Issue
- 发送邮件到 [<EMAIL>]

## 更新日志

### v0.1.0 (开发中)
- 项目初始化
- 基础架构搭建
- 核心功能开发
