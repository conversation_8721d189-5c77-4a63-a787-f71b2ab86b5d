# 征信报告处理工具 - 环境变量配置示例
# 复制此文件为 .env 并填入真实的配置值

# ===================
# AI服务配置
# ===================
# Google Gemini API
GEMINI_API_KEY=your_gemini_api_key_here

# Moonshot Kimi API (备选)
KIMI_API_KEY=your_kimi_api_key_here

# OpenAI API (备选)
OPENAI_API_KEY=your_openai_api_key_here

# ===================
# 飞书配置
# ===================
# 飞书应用配置
FEISHU_APP_ID=cli_a8cd1d55e7f7901c
FEISHU_APP_SECRET=mzCZdAxwQLVwkt8lOpG3Ib4e1kZ7Tcdl

# 飞书多维表格配置
FEISHU_APP_TOKEN=OJHTbcz95ad2q3szuWJcCRJLnCb
FEISHU_TABLE_ID=tbliPO5u586c362N
FEISHU_COMPANY_COL=company
FEISHU_JSON_COL=JSON

# ===================
# 应用配置
# ===================
# 服务器配置
HOST=0.0.0.0
PORT=8000
DEBUG=True

# 文件上传限制
MAX_FILE_SIZE=10485760      # 10MB (10 * 1024 * 1024)
MAX_BATCH_SIZE=52428800     # 50MB (50 * 1024 * 1024)
MAX_FILES_PER_BATCH=10

# 处理配置
AI_REQUEST_TIMEOUT=120      # AI API请求超时时间(秒)
FEISHU_REQUEST_TIMEOUT=30   # 飞书API请求超时时间(秒)
MAX_RETRY_ATTEMPTS=3        # 最大重试次数

# ===================
# 日志配置
# ===================
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# ===================
# 安全配置
# ===================
SECRET_KEY=your_secret_key_here_change_in_production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
