"""
征信报告处理工具 - API路由模块
"""
from fastapi import APIRouter, HTTPException, UploadFile, File, Form, Depends
from fastapi.responses import JSONResponse
from typing import List, Optional
import logging
from datetime import datetime

from app.config.settings import get_settings, Settings
from app.services.pdf_service import pdf_processor
from app.models.schemas import (
    SystemInfoResponse, FileValidationResult, PDFExtractionResult,
    BatchProcessingResult, ReportType
)

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter()


@router.get("/info", summary="获取系统信息")
async def get_system_info(settings: Settings = Depends(get_settings)):
    """获取系统配置和限制信息"""
    try:
        return {
            "system": {
                "name": "征信报告处理工具",
                "version": "1.0.0",
                "environment": "development" if settings.DEBUG else "production"
            },
            "upload_limits": settings.get_upload_limits_info(),
            "ai_providers": settings.get_ai_providers(),
            "supported_report_types": [
                {
                    "type": "personal",
                    "name": "个人征信报告",
                    "description": "处理个人信用报告PDF文件"
                },
                {
                    "type": "corporate",
                    "name": "企业征信报告",
                    "description": "处理企业信用报告PDF文件"
                }
            ]
        }
    except Exception as e:
        logger.error(f"获取系统信息失败: {e}")
        raise HTTPException(status_code=500, detail="获取系统信息失败")


@router.post("/validate-files", summary="验证上传文件")
async def validate_files(
    files: List[UploadFile] = File(...),
    report_type: str = Form(...),
    settings: Settings = Depends(get_settings)
):
    """验证上传的文件是否符合要求"""
    try:
        # 验证报告类型
        if report_type not in ["personal", "corporate"]:
            raise HTTPException(
                status_code=400,
                detail="无效的报告类型，支持的类型: personal, corporate"
            )

        # 验证文件数量
        if len(files) > settings.MAX_FILES_PER_BATCH:
            raise HTTPException(
                status_code=400,
                detail=f"文件数量超过限制，最多允许{settings.MAX_FILES_PER_BATCH}个文件"
            )

        # 使用PDF处理器验证每个文件
        total_size = 0
        file_info = []

        for file in files:
            # 使用PDF处理器进行详细验证
            validation_result = await pdf_processor.validate_pdf_file(file)
            file_info.append(validation_result)
            total_size += validation_result["size_bytes"]

        # 验证批次总大小
        is_valid, message = settings.validate_batch_size(total_size, len(files))
        if not is_valid:
            raise HTTPException(status_code=400, detail=message)

        return {
            "status": "valid",
            "message": "所有文件验证通过",
            "report_type": report_type,
            "file_count": len(files),
            "total_size_mb": round(total_size / 1024 / 1024, 2),
            "files": file_info,
            "estimated_processing_time": f"{len(files) * 30}-{len(files) * 60}秒"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"文件验证失败: {e}")
        raise HTTPException(status_code=500, detail="文件验证过程中发生错误")


@router.post("/extract-text", summary="提取PDF文本内容")
async def extract_pdf_text(
    files: List[UploadFile] = File(...),
    report_type: str = Form(...),
    settings: Settings = Depends(get_settings)
):
    """提取PDF文件的文本内容（用于测试和调试）"""
    try:
        # 验证报告类型
        if report_type not in ["personal", "corporate"]:
            raise HTTPException(
                status_code=400,
                detail="无效的报告类型，支持的类型: personal, corporate"
            )

        # 使用PDF处理器批量提取文本
        result = await pdf_processor.batch_process_files(files)

        return {
            "status": "completed",
            "message": "PDF文本提取完成",
            "report_type": report_type,
            "extraction_results": result
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"PDF文本提取失败: {e}")
        raise HTTPException(status_code=500, detail="PDF文本提取过程中发生错误")


@router.post("/process-reports", summary="处理征信报告")
async def process_reports(
    files: List[UploadFile] = File(...),
    report_type: str = Form(...),
    settings: Settings = Depends(get_settings)
):
    """处理上传的征信报告文件"""
    try:
        # 首先验证文件
        validation_result = await validate_files(files, report_type, settings)

        # TODO: 实现实际的处理逻辑
        # 1. PDF文件处理
        # 2. AI服务调用
        # 3. 飞书API集成
        # 4. 错误处理和重试

        # 临时返回模拟结果
        processing_results = []
        for i, file in enumerate(files):
            processing_results.append({
                "filename": file.filename,
                "status": "pending",
                "message": "等待处理",
                "progress": 0,
                "start_time": datetime.now().isoformat()
            })

        return {
            "status": "accepted",
            "message": "文件已接收，开始处理",
            "batch_id": f"batch_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            "report_type": report_type,
            "file_count": len(files),
            "estimated_completion": f"{len(files) * 45}秒后",
            "results": processing_results
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"处理征信报告失败: {e}")
        raise HTTPException(status_code=500, detail="处理征信报告时发生错误")


@router.get("/status/{batch_id}", summary="查询处理状态")
async def get_processing_status(batch_id: str):
    """查询批次处理状态"""
    try:
        # TODO: 实现实际的状态查询逻辑
        # 从数据库或缓存中获取处理状态

        # 临时返回模拟状态
        return {
            "batch_id": batch_id,
            "status": "processing",
            "progress": 45,
            "completed_files": 2,
            "total_files": 5,
            "estimated_remaining": "120秒",
            "results": [
                {
                    "filename": "report1.pdf",
                    "status": "completed",
                    "progress": 100,
                    "message": "处理完成"
                },
                {
                    "filename": "report2.pdf",
                    "status": "processing",
                    "progress": 75,
                    "message": "正在调用AI服务"
                }
            ]
        }

    except Exception as e:
        logger.error(f"查询处理状态失败: {e}")
        raise HTTPException(status_code=500, detail="查询处理状态时发生错误")
