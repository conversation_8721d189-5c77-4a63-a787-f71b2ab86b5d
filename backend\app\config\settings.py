"""
征信报告处理工具 - 配置管理
"""
from pydantic_settings import BaseSettings
from pydantic import Field
from typing import Optional
from functools import lru_cache
import os


class Settings(BaseSettings):
    """应用配置类"""

    # 应用基础配置
    HOST: str = Field(default="0.0.0.0", description="服务器主机地址")
    PORT: int = Field(default=8000, description="服务器端口")
    DEBUG: bool = Field(default=True, description="调试模式")

    # AI服务配置
    GEMINI_API_KEY: Optional[str] = Field(default=None, description="Google Gemini API密钥")
    KIMI_API_KEY: Optional[str] = Field(default=None, description="Moonshot Kimi API密钥")
    OPENAI_API_KEY: Optional[str] = Field(default=None, description="OpenAI API密钥")

    # 飞书配置
    FEISHU_APP_ID: str = Field(description="飞书应用ID")
    FEISHU_APP_SECRET: str = Field(description="飞书应用密钥")
    FEISHU_APP_TOKEN: str = Field(description="飞书多维表格Token")
    FEISHU_TABLE_ID: str = Field(description="飞书表格ID")
    FEISHU_COMPANY_COL: str = Field(default="company", description="公司名称列名")
    FEISHU_JSON_COL: str = Field(default="JSON", description="JSON数据列名")

    # 文件上传限制
    MAX_FILE_SIZE: int = Field(default=10485760, description="单个文件最大大小(字节) - 10MB")
    MAX_BATCH_SIZE: int = Field(default=52428800, description="批次最大大小(字节) - 50MB")
    MAX_FILES_PER_BATCH: int = Field(default=10, description="批次最大文件数")

    # 处理配置
    AI_REQUEST_TIMEOUT: int = Field(default=120, description="AI API请求超时时间(秒)")
    FEISHU_REQUEST_TIMEOUT: int = Field(default=30, description="飞书API请求超时时间(秒)")
    MAX_RETRY_ATTEMPTS: int = Field(default=3, description="最大重试次数")

    # 日志配置
    LOG_LEVEL: str = Field(default="INFO", description="日志级别")
    LOG_FILE: str = Field(default="logs/app.log", description="日志文件路径")

    # 安全配置
    SECRET_KEY: str = Field(default="your_secret_key_here_change_in_production", description="密钥")
    ALGORITHM: str = Field(default="HS256", description="加密算法")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(default=30, description="访问令牌过期时间(分钟)")

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True

    def get_ai_providers(self) -> list:
        """获取可用的AI服务提供商列表"""
        providers = []
        if self.GEMINI_API_KEY:
            providers.append({"name": "gemini", "priority": 1, "available": True})
        if self.KIMI_API_KEY:
            providers.append({"name": "kimi", "priority": 2, "available": True})
        if self.OPENAI_API_KEY:
            providers.append({"name": "openai", "priority": 3, "available": True})
        return sorted(providers, key=lambda x: x["priority"])

    def validate_file_size(self, file_size: int) -> bool:
        """验证文件大小是否符合限制"""
        return file_size <= self.MAX_FILE_SIZE

    def validate_batch_size(self, total_size: int, file_count: int) -> tuple[bool, str]:
        """验证批次大小和文件数量是否符合限制"""
        if file_count > self.MAX_FILES_PER_BATCH:
            return False, f"批次文件数量超过限制，最多允许{self.MAX_FILES_PER_BATCH}个文件"

        if total_size > self.MAX_BATCH_SIZE:
            return False, f"批次总大小超过限制，最大允许{self.MAX_BATCH_SIZE / 1024 / 1024:.1f}MB"

        return True, "验证通过"

    def get_upload_limits_info(self) -> dict:
        """获取上传限制信息"""
        return {
            "max_file_size_mb": self.MAX_FILE_SIZE / 1024 / 1024,
            "max_batch_size_mb": self.MAX_BATCH_SIZE / 1024 / 1024,
            "max_files_per_batch": self.MAX_FILES_PER_BATCH,
            "supported_formats": [".pdf"],
            "processing_time_per_file": "30-60秒"
        }


@lru_cache()
def get_settings() -> Settings:
    """获取配置实例（单例模式）"""
    return Settings()
