"""
征信报告处理工具 - FastAPI应用入口
"""
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
import logging
from contextlib import asynccontextmanager

from app.config.settings import get_settings
from app.api.routes import router as api_router

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

settings = get_settings()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logger.info("🚀 征信报告处理工具启动中...")
    logger.info(f"📊 环境: {'开发' if settings.DEBUG else '生产'}")
    logger.info(f"🌐 服务地址: http://{settings.HOST}:{settings.PORT}")

    yield

    # 关闭时执行
    logger.info("🛑 征信报告处理工具正在关闭...")


# 创建FastAPI应用实例
app = FastAPI(
    title="征信报告处理工具",
    description="自动化处理个人和企业征信报告，提取数据并存储到飞书多维表格",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# 配置CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 开发环境允许所有来源
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/", tags=["健康检查"])
async def root():
    """根路径 - 服务状态检查"""
    return {
        "message": "征信报告处理工具 API 服务正常运行",
        "version": "1.0.0",
        "status": "healthy",
        "docs": "/docs"
    }


@app.get("/health", tags=["健康检查"])
async def health_check():
    """健康检查端点"""
    try:
        return {
            "status": "healthy",
            "timestamp": "2024-08-25T00:00:00Z",
            "version": "1.0.0",
            "environment": "development" if settings.DEBUG else "production",
            "services": {
                "api": "running",
                "database": "connected",  # 后续添加实际检查
                "ai_service": "available",  # 后续添加实际检查
                "feishu_api": "available"  # 后续添加实际检查
            }
        }
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        raise HTTPException(status_code=503, detail="服务不可用")


# 注册API路由
app.include_router(api_router, prefix="/api", tags=["API"])


# 全局异常处理
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """全局异常处理器"""
    logger.error(f"未处理的异常: {exc}")
    return JSONResponse(
        status_code=500,
        content={
            "error": "内部服务器错误",
            "message": "服务器遇到了一个错误，请稍后重试",
            "detail": str(exc) if settings.DEBUG else None
        }
    )


if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info"
    )
