"""
征信报告处理工具 - 数据模型
"""
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum


class ReportType(str, Enum):
    """报告类型枚举"""
    PERSONAL = "personal"
    CORPORATE = "corporate"


class ProcessingStatus(str, Enum):
    """处理状态枚举"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class ExtractionQuality(str, Enum):
    """提取质量等级"""
    EXCELLENT = "excellent"
    GOOD = "good"
    FAIR = "fair"
    POOR = "poor"
    FAILED = "failed"


class FileValidationResult(BaseModel):
    """文件验证结果"""
    filename: str
    size_bytes: int
    size_mb: float
    page_count: Optional[int] = None
    status: str
    message: str


class ExtractionMetrics(BaseModel):
    """文本提取指标"""
    char_count: int
    line_count: int
    word_count: int
    chinese_ratio: float
    whitespace_ratio: float


class ExtractionQualityAssessment(BaseModel):
    """文本提取质量评估"""
    score: int = Field(..., ge=0, le=100, description="质量评分 (0-100)")
    level: ExtractionQuality
    metrics: ExtractionMetrics
    issues: List[str] = Field(default_factory=list)
    suggestions: List[str] = Field(default_factory=list)


class ExtractionMethodResult(BaseModel):
    """提取方法结果"""
    text_length: int
    success: bool


class PDFExtractionResult(BaseModel):
    """PDF文本提取结果"""
    filename: str
    page_count: int
    extraction_methods: Dict[str, ExtractionMethodResult]
    extracted_text: str
    text_length: int
    extraction_quality: ExtractionQualityAssessment
    status: str
    message: str
    file_index: Optional[int] = None
    processing_order: Optional[int] = None


class BatchProcessingSummary(BaseModel):
    """批量处理摘要"""
    total_files: int
    successful_files: int
    failed_files: int
    total_size_mb: float
    success_rate: float


class BatchProcessingResult(BaseModel):
    """批量处理结果"""
    batch_summary: BatchProcessingSummary
    results: List[PDFExtractionResult]
    status: str
    message: str


class ProcessingTask(BaseModel):
    """处理任务"""
    task_id: str
    batch_id: str
    report_type: ReportType
    filename: str
    status: ProcessingStatus
    progress: int = Field(default=0, ge=0, le=100)
    message: str = ""
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    error_message: Optional[str] = None
    result: Optional[Dict[str, Any]] = None


class BatchTask(BaseModel):
    """批次任务"""
    batch_id: str
    report_type: ReportType
    total_files: int
    completed_files: int = 0
    failed_files: int = 0
    status: ProcessingStatus = ProcessingStatus.PENDING
    progress: int = Field(default=0, ge=0, le=100)
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    estimated_completion_time: Optional[str] = None
    tasks: List[ProcessingTask] = Field(default_factory=list)


class SystemInfo(BaseModel):
    """系统信息"""
    name: str
    version: str
    environment: str


class UploadLimits(BaseModel):
    """上传限制信息"""
    max_file_size_mb: float
    max_batch_size_mb: float
    max_files_per_batch: int
    supported_formats: List[str]
    processing_time_per_file: str


class AIProvider(BaseModel):
    """AI服务提供商"""
    name: str
    priority: int
    available: bool


class SupportedReportType(BaseModel):
    """支持的报告类型"""
    type: str
    name: str
    description: str


class SystemInfoResponse(BaseModel):
    """系统信息响应"""
    system: SystemInfo
    upload_limits: UploadLimits
    ai_providers: List[AIProvider]
    supported_report_types: List[SupportedReportType]


class HealthCheckResponse(BaseModel):
    """健康检查响应"""
    status: str
    timestamp: str
    version: str
    environment: str
    services: Dict[str, str]


class ErrorResponse(BaseModel):
    """错误响应"""
    error: str
    message: str
    detail: Optional[str] = None
    timestamp: Optional[str] = None
