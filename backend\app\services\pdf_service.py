"""
征信报告处理工具 - PDF处理服务
"""
import io
import os
import tempfile
import logging
from typing import Optional, Dict, Any, List
from pathlib import Path

import PyPDF2
import pdfplumber
from fastapi import UploadFile, HTTPException

from app.config.settings import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


class PDFProcessor:
    """PDF文件处理器"""

    def __init__(self):
        self.temp_dir = tempfile.gettempdir()
        logger.info(f"PDF处理器初始化，临时目录: {self.temp_dir}")

    async def validate_pdf_file(self, file: UploadFile) -> Dict[str, Any]:
        """验证PDF文件的有效性"""
        try:
            # 检查文件扩展名
            if not file.filename.lower().endswith('.pdf'):
                raise HTTPException(
                    status_code=400,
                    detail=f"不支持的文件格式: {file.filename}，仅支持PDF文件"
                )

            # 读取文件内容
            content = await file.read()
            file_size = len(content)

            # 验证文件大小
            if not settings.validate_file_size(file_size):
                raise HTTPException(
                    status_code=400,
                    detail=f"文件 {file.filename} 大小超过限制，最大允许{settings.MAX_FILE_SIZE / 1024 / 1024:.1f}MB"
                )

            # 验证PDF文件结构
            try:
                pdf_reader = PyPDF2.PdfReader(io.BytesIO(content))
                page_count = len(pdf_reader.pages)

                if page_count == 0:
                    raise HTTPException(
                        status_code=400,
                        detail=f"PDF文件 {file.filename} 没有有效页面"
                    )

                # 重置文件指针
                await file.seek(0)

                return {
                    "filename": file.filename,
                    "size_bytes": file_size,
                    "size_mb": round(file_size / 1024 / 1024, 2),
                    "page_count": page_count,
                    "status": "valid",
                    "message": "PDF文件验证通过"
                }

            except Exception as e:
                logger.error(f"PDF文件结构验证失败: {e}")
                raise HTTPException(
                    status_code=400,
                    detail=f"PDF文件 {file.filename} 格式损坏或无法读取"
                )

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"PDF文件验证过程中发生错误: {e}")
            raise HTTPException(
                status_code=500,
                detail=f"验证PDF文件时发生内部错误"
            )

    async def extract_text_content(self, file: UploadFile) -> Dict[str, Any]:
        """提取PDF文件的文本内容"""
        try:
            # 先验证文件
            validation_result = await self.validate_pdf_file(file)

            # 读取文件内容
            content = await file.read()

            # 使用PyPDF2提取基础文本
            pypdf2_text = self._extract_with_pypdf2(content)

            # 使用pdfplumber提取详细文本
            pdfplumber_text = self._extract_with_pdfplumber(content)

            # 选择最佳提取结果
            best_text = self._select_best_extraction(pypdf2_text, pdfplumber_text)

            # 重置文件指针
            await file.seek(0)

            return {
                "filename": file.filename,
                "page_count": validation_result["page_count"],
                "extraction_methods": {
                    "pypdf2": {
                        "text_length": len(pypdf2_text),
                        "success": len(pypdf2_text) > 0
                    },
                    "pdfplumber": {
                        "text_length": len(pdfplumber_text),
                        "success": len(pdfplumber_text) > 0
                    }
                },
                "extracted_text": best_text,
                "text_length": len(best_text),
                "extraction_quality": self._assess_extraction_quality(best_text),
                "status": "success" if len(best_text) > 0 else "failed",
                "message": "文本提取完成" if len(best_text) > 0 else "未能提取到有效文本"
            }

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"PDF文本提取失败: {e}")
            raise HTTPException(
                status_code=500,
                detail=f"提取PDF文本时发生错误: {str(e)}"
            )

    def _extract_with_pypdf2(self, content: bytes) -> str:
        """使用PyPDF2提取文本"""
        try:
            pdf_reader = PyPDF2.PdfReader(io.BytesIO(content))
            text_parts = []

            for page_num, page in enumerate(pdf_reader.pages):
                try:
                    page_text = page.extract_text()
                    if page_text.strip():
                        text_parts.append(f"--- 第{page_num + 1}页 ---\n{page_text}\n")
                except Exception as e:
                    logger.warning(f"PyPDF2提取第{page_num + 1}页失败: {e}")
                    continue

            return "\n".join(text_parts)

        except Exception as e:
            logger.error(f"PyPDF2文本提取失败: {e}")
            return ""

    def _extract_with_pdfplumber(self, content: bytes) -> str:
        """使用pdfplumber提取文本"""
        try:
            text_parts = []

            with pdfplumber.open(io.BytesIO(content)) as pdf:
                for page_num, page in enumerate(pdf.pages):
                    try:
                        page_text = page.extract_text()
                        if page_text and page_text.strip():
                            text_parts.append(f"--- 第{page_num + 1}页 ---\n{page_text}\n")
                    except Exception as e:
                        logger.warning(f"pdfplumber提取第{page_num + 1}页失败: {e}")
                        continue

            return "\n".join(text_parts)

        except Exception as e:
            logger.error(f"pdfplumber文本提取失败: {e}")
            return ""

    def _select_best_extraction(self, pypdf2_text: str, pdfplumber_text: str) -> str:
        """选择最佳的文本提取结果"""
        # 如果两个都为空，返回空字符串
        if not pypdf2_text.strip() and not pdfplumber_text.strip():
            return ""

        # 如果只有一个有内容，返回有内容的
        if not pypdf2_text.strip():
            return pdfplumber_text
        if not pdfplumber_text.strip():
            return pypdf2_text

        # 两个都有内容，选择更长的（通常意味着提取更完整）
        if len(pdfplumber_text) > len(pypdf2_text):
            logger.info("选择pdfplumber提取结果（内容更丰富）")
            return pdfplumber_text
        else:
            logger.info("选择PyPDF2提取结果")
            return pypdf2_text

    def _assess_extraction_quality(self, text: str) -> Dict[str, Any]:
        """评估文本提取质量"""
        if not text.strip():
            return {
                "score": 0,
                "level": "failed",
                "issues": ["未提取到任何文本"],
                "suggestions": ["检查PDF文件是否为扫描件", "尝试OCR处理"]
            }

        # 基础质量指标
        char_count = len(text)
        line_count = len(text.split('\n'))
        word_count = len(text.split())

        # 检查常见问题
        issues = []
        suggestions = []

        # 检查是否有中文字符
        chinese_chars = sum(1 for char in text if '\u4e00' <= char <= '\u9fff')
        chinese_ratio = chinese_chars / char_count if char_count > 0 else 0

        if chinese_ratio < 0.1:
            issues.append("中文字符比例较低，可能存在编码问题")
            suggestions.append("检查PDF文件编码格式")

        # 检查是否有过多的空白字符
        whitespace_ratio = sum(1 for char in text if char.isspace()) / char_count if char_count > 0 else 0
        if whitespace_ratio > 0.5:
            issues.append("空白字符比例过高")
            suggestions.append("可能需要文本清理处理")

        # 评分逻辑
        score = 0
        if char_count > 100:
            score += 30
        if word_count > 50:
            score += 30
        if chinese_ratio > 0.3:
            score += 40

        # 确定质量等级
        if score >= 80:
            level = "excellent"
        elif score >= 60:
            level = "good"
        elif score >= 40:
            level = "fair"
        elif score >= 20:
            level = "poor"
        else:
            level = "failed"

        return {
            "score": score,
            "level": level,
            "metrics": {
                "char_count": char_count,
                "line_count": line_count,
                "word_count": word_count,
                "chinese_ratio": round(chinese_ratio, 3),
                "whitespace_ratio": round(whitespace_ratio, 3)
            },
            "issues": issues,
            "suggestions": suggestions
        }

    async def save_temp_file(self, file: UploadFile) -> str:
        """保存临时文件并返回文件路径"""
        try:
            # 创建临时文件
            temp_file = tempfile.NamedTemporaryFile(
                delete=False,
                suffix=f"_{file.filename}",
                dir=self.temp_dir
            )

            # 写入文件内容
            content = await file.read()
            temp_file.write(content)
            temp_file.close()

            # 重置文件指针
            await file.seek(0)

            logger.info(f"临时文件已保存: {temp_file.name}")
            return temp_file.name

        except Exception as e:
            logger.error(f"保存临时文件失败: {e}")
            raise HTTPException(
                status_code=500,
                detail="保存临时文件时发生错误"
            )

    def cleanup_temp_file(self, file_path: str) -> bool:
        """清理临时文件"""
        try:
            if os.path.exists(file_path):
                os.unlink(file_path)
                logger.info(f"临时文件已清理: {file_path}")
                return True
            return False
        except Exception as e:
            logger.error(f"清理临时文件失败: {e}")
            return False

    async def batch_process_files(self, files: List[UploadFile]) -> Dict[str, Any]:
        """批量处理PDF文件"""
        try:
            results = []
            total_size = 0

            for i, file in enumerate(files):
                logger.info(f"处理文件 {i+1}/{len(files)}: {file.filename}")

                try:
                    # 提取文本内容
                    result = await self.extract_text_content(file)
                    result["file_index"] = i + 1
                    result["processing_order"] = i + 1
                    results.append(result)
                    total_size += result.get("size_bytes", 0)

                except Exception as e:
                    logger.error(f"处理文件 {file.filename} 失败: {e}")
                    results.append({
                        "filename": file.filename,
                        "file_index": i + 1,
                        "processing_order": i + 1,
                        "status": "failed",
                        "error": str(e),
                        "message": f"文件处理失败: {str(e)}"
                    })

            # 统计处理结果
            successful_files = [r for r in results if r.get("status") == "success"]
            failed_files = [r for r in results if r.get("status") == "failed"]

            return {
                "batch_summary": {
                    "total_files": len(files),
                    "successful_files": len(successful_files),
                    "failed_files": len(failed_files),
                    "total_size_mb": round(total_size / 1024 / 1024, 2),
                    "success_rate": round(len(successful_files) / len(files) * 100, 1) if files else 0
                },
                "results": results,
                "status": "completed",
                "message": f"批量处理完成，成功: {len(successful_files)}, 失败: {len(failed_files)}"
            }

        except Exception as e:
            logger.error(f"批量处理PDF文件失败: {e}")
            raise HTTPException(
                status_code=500,
                detail=f"批量处理PDF文件时发生错误: {str(e)}"
            )


# 创建全局PDF处理器实例
pdf_processor = PDFProcessor()
