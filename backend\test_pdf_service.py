"""
PDF处理服务测试脚本
"""
import asyncio
import io
import logging
from fastapi import UploadFile

# 设置日志
logging.basicConfig(level=logging.INFO)

# 导入PDF处理器
from app.services.pdf_service import pdf_processor

# 创建一个简单的测试PDF内容
def create_test_pdf_content():
    """创建一个简单的测试PDF内容"""
    # 这里我们创建一个最小的PDF文件内容
    # 实际使用中，这应该是真实的PDF文件
    pdf_content = b"""%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj

4 0 obj
<<
/Length 44
>>
stream
BT
/F1 12 Tf
100 700 Td
(Hello World) Tj
ET
endstream
endobj

xref
0 5
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000206 00000 n 
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
300
%%EOF"""
    return pdf_content


class MockUploadFile:
    """模拟UploadFile对象"""
    def __init__(self, filename: str, content: bytes):
        self.filename = filename
        self.content = content
        self.file = io.BytesIO(content)
        self.size = len(content)
    
    async def read(self):
        return self.content
    
    async def seek(self, position: int):
        self.file.seek(position)


async def test_pdf_validation():
    """测试PDF文件验证功能"""
    print("\n=== 测试PDF文件验证功能 ===")
    
    # 创建测试文件
    test_content = create_test_pdf_content()
    mock_file = MockUploadFile("test_report.pdf", test_content)
    
    try:
        result = await pdf_processor.validate_pdf_file(mock_file)
        print(f"✅ 验证成功: {result}")
        return True
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False


async def test_text_extraction():
    """测试文本提取功能"""
    print("\n=== 测试文本提取功能 ===")
    
    # 创建测试文件
    test_content = create_test_pdf_content()
    mock_file = MockUploadFile("test_report.pdf", test_content)
    
    try:
        result = await pdf_processor.extract_text_content(mock_file)
        print(f"✅ 文本提取成功:")
        print(f"   文件名: {result['filename']}")
        print(f"   页数: {result['page_count']}")
        print(f"   文本长度: {result['text_length']}")
        print(f"   提取质量: {result['extraction_quality']['level']}")
        print(f"   状态: {result['status']}")
        if result['extracted_text']:
            print(f"   提取的文本: {result['extracted_text'][:100]}...")
        return True
    except Exception as e:
        print(f"❌ 文本提取失败: {e}")
        return False


async def test_batch_processing():
    """测试批量处理功能"""
    print("\n=== 测试批量处理功能 ===")
    
    # 创建多个测试文件
    test_files = []
    for i in range(3):
        test_content = create_test_pdf_content()
        mock_file = MockUploadFile(f"test_report_{i+1}.pdf", test_content)
        test_files.append(mock_file)
    
    try:
        result = await pdf_processor.batch_process_files(test_files)
        print(f"✅ 批量处理成功:")
        print(f"   总文件数: {result['batch_summary']['total_files']}")
        print(f"   成功文件数: {result['batch_summary']['successful_files']}")
        print(f"   失败文件数: {result['batch_summary']['failed_files']}")
        print(f"   成功率: {result['batch_summary']['success_rate']}%")
        return True
    except Exception as e:
        print(f"❌ 批量处理失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("🚀 开始PDF处理服务测试...")
    
    tests = [
        ("PDF文件验证", test_pdf_validation),
        ("文本提取", test_text_extraction),
        ("批量处理", test_batch_processing)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 运行测试: {test_name}")
        try:
            success = await test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ 测试 {test_name} 发生异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果摘要
    print("\n" + "="*50)
    print("📊 测试结果摘要:")
    print("="*50)
    
    passed = 0
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"   {test_name}: {status}")
        if success:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！PDF处理服务工作正常。")
    else:
        print("⚠️  部分测试失败，请检查PDF处理服务。")


if __name__ == "__main__":
    asyncio.run(main())
