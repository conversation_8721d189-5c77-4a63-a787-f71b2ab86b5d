# 征信报告处理工具 - 项目上下文中枢

## 🎯 当前状态 (最高优先级)

### 当前任务
- **正在进行**: 后端基础框架开发
- **当前阶段**: 第一阶段 MVP开发 (2-3周)
- **下一步**: 创建FastAPI应用入口和配置管理

### 进度概览
- ✅ **项目初始化完成** (1天) - 100%
  - 项目目录结构已建立
  - Git仓库已初始化
  - Python虚拟环境已创建
  - 核心依赖包已安装 (FastAPI, Uvicorn, Requests等)
  - 环境变量配置模板已创建

- 🔄 **后端基础框架** (2-3天) - 0%
  - 待开始: FastAPI应用入口创建
  - 待开始: CORS中间件配置
  - 待开始: 配置管理实现

### 即将开始的工作
1. 创建FastAPI应用入口 (main.py)
2. 实现配置管理 (settings.py)
3. 创建基础API路由结构

## 🔧 技术决策 (最高优先级)

### 核心技术栈
- **后端框架**: Python + FastAPI (已确定)
  - 理由: 异步处理能力强，文件上传支持优秀，自动API文档生成
- **前端技术**: 原生HTML/CSS/JavaScript + Tailwind CSS
  - 理由: 需求简单，基于现有代码，加载速度快
- **AI服务**: Google Gemini 1.5 Pro (主要) + Moonshot Kimi + OpenAI (备选)
  - 理由: 多服务商支持，自动降级机制

### 关键架构决策
- **数据存储**: 飞书多维表格 (统一JSON列存储)
- **文件处理**: 直接新增记录，无需查重
- **错误处理**: 支持失败重试机制
- **部署方式**: 先本地部署，后续支持多用户

### 重要约定
- 单个PDF文件限制: 10MB
- 批次文件数限制: 最多10个
- 处理时间预期: 30-60秒/文件
- 个人和企业征信使用同一飞书表格

## 📋 任务管理

### 第一阶段 - MVP开发 (2-3周)

#### 已完成 ✅
- [x] 项目初始化 (1天)
  - [x] 创建项目目录结构
  - [x] 初始化Git仓库
  - [x] 创建Python虚拟环境
  - [x] 安装基础依赖包
  - [x] 配置.env.example文件
  - [x] 编写项目README.md

#### 进行中 🔄
- [ ] 后端基础框架 (2-3天)
  - [ ] 创建FastAPI应用入口 (main.py)
  - [ ] 配置CORS中间件
  - [ ] 实现配置管理 (settings.py)
  - [ ] 创建基础API路由结构
  - [ ] 实现健康检查端点
  - [ ] 配置日志系统
  - [ ] 实现文件上传端点基础结构

#### 待开始 ⏳
- [ ] PDF处理服务 (2天)
- [ ] AI服务集成 (3-4天)
- [ ] 飞书API集成 (2-3天)
- [ ] 核心业务逻辑 (2-3天)
- [ ] 前端界面开发 (3-4天)
- [ ] API接口开发 (2天)
- [ ] 测试和调试 (2-3天)

### 项目里程碑
- [ ] **里程碑1**: MVP版本完成 (3周后)
- [ ] **里程碑2**: 增强版本完成 (5周后)  
- [ ] **里程碑3**: 多用户版本完成 (8周后)

## 🏗️ 项目架构

### 目录结构
```
credit-report-processor/
├── backend/
│   ├── app/
│   │   ├── main.py              # FastAPI应用入口
│   │   ├── api/routes.py        # API路由
│   │   ├── services/            # 业务服务层
│   │   │   ├── ai_service.py    # AI API调用服务
│   │   │   ├── feishu_service.py # 飞书API服务
│   │   │   └── pdf_service.py   # PDF处理服务
│   │   ├── config/              # 配置管理
│   │   │   ├── settings.py      # 配置管理
│   │   │   └── prompts.py       # AI提示词配置
│   │   ├── models/schemas.py    # 数据模型
│   │   └── utils/               # 工具函数
│   ├── requirements.txt
│   └── .env.example
├── frontend/
│   ├── index.html              # 主页面
│   └── assets/                 # 静态资源
├── 需求/                       # 需求文档
└── project-context.md         # 项目上下文中枢
```

### 核心流程
```
PDF文件 → AI提取 → JSON验证 → 飞书存储 → 状态反馈
```

### 数据流设计
1. 用户选择报告类型（个人/企业）
2. 上传PDF文件（支持多文件）
3. 后端接收文件并进行基础验证
4. 调用AI API提取数据
5. 验证返回的JSON格式
6. 直接新增记录到飞书表格
7. 返回处理结果给前端

## 📝 开发规范

### 代码风格
- Python: 遵循PEP 8规范
- JavaScript: 使用ES6+语法
- 文件命名: 小写字母+下划线

### 命名约定
- 变量: snake_case
- 函数: snake_case
- 类: PascalCase
- 常量: UPPER_CASE

### Git工作流
- 主分支: master
- 功能分支: feature/功能名
- 提交信息: 中文描述，简洁明确

## 📚 变更记录

### 2024-08-25
- ✅ **项目初始化完成**
  - 创建完整的项目目录结构
  - 初始化Git仓库并完成首次提交
  - 建立Python虚拟环境
  - 安装核心依赖: FastAPI 0.116.1, Uvicorn 0.35.0, Requests 2.32.5等
  - 创建环境变量配置模板
  - 编写项目README文档

### 架构决策记录
- **技术栈选择**: 选择FastAPI而非Flask，因为异步处理能力和自动文档生成
- **AI服务策略**: 采用多服务商支持策略，Gemini为主，Kimi和OpenAI为备选
- **数据存储方案**: 使用飞书多维表格，个人和企业征信数据存储在同一表格的JSON列中

## ⚠️ 风险与约定

### 已知风险
- AI API调用可能失败或超时
- PDF文件格式多样性可能影响解析
- 飞书API限流可能影响批量处理

### 特殊约定
- 每次AI调用都自动加载此上下文文件
- 上下文更新由AI自动完成，但需明确显示更新内容
- 重点关注当前任务状态和技术决策
- 版本管理与代码版本同步

### 注意事项
- 环境变量必须正确配置才能运行
- PDF文件大小限制需要前端验证
- 错误处理需要用户友好的提示信息
