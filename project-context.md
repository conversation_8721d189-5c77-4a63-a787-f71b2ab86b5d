# 征信报告处理工具 - 项目上下文中枢

## 🎯 当前状态 (最高优先级)

### 当前任务
- **正在进行**: AI服务集成开发
- **当前阶段**: 第一阶段 MVP开发 (2-3周)
- **下一步**: 安装Google Gemini API客户端并实现AI服务调用

### 进度概览
- ✅ **项目初始化完成** (1天) - 100%
  - 项目目录结构已建立
  - Git仓库已初始化
  - Python虚拟环境已创建
  - 核心依赖包已安装 (FastAPI, Uvicorn, Requests等)
  - 环境变量配置模板已创建

- ✅ **后端基础框架** (2-3天) - 100%
  - FastAPI应用入口已创建 (main.py)
  - CORS中间件已配置
  - 配置管理已实现 (settings.py)
  - 基础API路由结构已建立
  - 健康检查端点已实现
  - 日志系统已配置

- ✅ **PDF处理服务** (2天) - 100%
  - PDF处理库已安装 (PyPDF2, pdfplumber)
  - PDF文件验证功能已实现
  - PDF文本提取功能已实现
  - 批量处理功能已实现
  - 文件大小和格式验证已完成
  - 临时文件管理已实现
  - 自我测试脚本已创建并通过

- 🔄 **AI服务集成** (3-4天) - 0%
  - 待开始: 安装Google Gemini API客户端
  - 待开始: 实现Gemini API调用服务
  - 待开始: 配置个人征信提示词

### 即将开始的工作
1. 安装Google Gemini API客户端
2. 实现Gemini API调用服务
3. 配置个人和企业征信提示词

## 🔧 技术决策 (最高优先级)

### 核心技术栈
- **后端框架**: Python + FastAPI (已确定)
  - 理由: 异步处理能力强，文件上传支持优秀，自动API文档生成
- **前端技术**: 原生HTML/CSS/JavaScript + Tailwind CSS
  - 理由: 需求简单，基于现有代码，加载速度快
- **AI服务**: Google Gemini 1.5 Pro (主要) + Moonshot Kimi + OpenAI (备选)
  - 理由: 多服务商支持，自动降级机制

### 关键架构决策
- **数据存储**: 飞书多维表格 (统一JSON列存储)
- **文件处理**: 直接新增记录，无需查重
- **错误处理**: 支持失败重试机制
- **部署方式**: 先本地部署，后续支持多用户

### 重要约定
- 单个PDF文件限制: 10MB
- 批次文件数限制: 最多10个
- 处理时间预期: 30-60秒/文件
- 个人和企业征信使用同一飞书表格

## 📋 任务管理

### 第一阶段 - MVP开发 (2-3周)

#### 已完成 ✅
- [x] 项目初始化 (1天)
  - [x] 创建项目目录结构
  - [x] 初始化Git仓库
  - [x] 创建Python虚拟环境
  - [x] 安装基础依赖包
  - [x] 配置.env.example文件
  - [x] 编写项目README.md

- [x] 后端基础框架 (2-3天)
  - [x] 创建FastAPI应用入口 (main.py)
  - [x] 配置CORS中间件
  - [x] 实现配置管理 (settings.py)
  - [x] 创建基础API路由结构
  - [x] 实现健康检查端点
  - [x] 配置日志系统
  - [x] 实现文件上传端点基础结构

- [x] PDF处理服务 (2天)
  - [x] 安装PDF处理库 (PyPDF2/pdfplumber)
  - [x] 实现PDF文件读取功能
  - [x] 实现PDF内容提取
  - [x] 添加文件格式验证
  - [x] 添加文件大小限制检查
  - [x] 实现临时文件管理
  - [x] 编写PDF处理单元测试

#### 进行中 🔄
- [ ] AI服务集成 (3-4天)
  - [ ] 安装Gemini API客户端
  - [ ] 实现Gemini API调用服务
  - [ ] 配置个人征信提示词
  - [ ] 配置企业征信提示词
  - [ ] 实现提示词选择逻辑
  - [ ] 添加API响应验证
  - [ ] 实现JSON格式校验
  - [ ] 添加AI服务错误处理
  - [ ] 编写AI服务单元测试

#### 待开始 ⏳
- [ ] 飞书API集成 (2-3天)
- [ ] 核心业务逻辑 (2-3天)
- [ ] 前端界面开发 (3-4天)
- [ ] API接口开发 (2天)
- [ ] 测试和调试 (2-3天)

### 项目里程碑
- [ ] **里程碑1**: MVP版本完成 (3周后)
- [ ] **里程碑2**: 增强版本完成 (5周后)  
- [ ] **里程碑3**: 多用户版本完成 (8周后)

## 🏗️ 项目架构

### 目录结构
```
credit-report-processor/
├── backend/
│   ├── app/
│   │   ├── main.py              # FastAPI应用入口
│   │   ├── api/routes.py        # API路由
│   │   ├── services/            # 业务服务层
│   │   │   ├── ai_service.py    # AI API调用服务
│   │   │   ├── feishu_service.py # 飞书API服务
│   │   │   └── pdf_service.py   # PDF处理服务
│   │   ├── config/              # 配置管理
│   │   │   ├── settings.py      # 配置管理
│   │   │   └── prompts.py       # AI提示词配置
│   │   ├── models/schemas.py    # 数据模型
│   │   └── utils/               # 工具函数
│   ├── requirements.txt
│   └── .env.example
├── frontend/
│   ├── index.html              # 主页面
│   └── assets/                 # 静态资源
├── 需求/                       # 需求文档
└── project-context.md         # 项目上下文中枢
```

### 核心流程
```
PDF文件 → AI提取 → JSON验证 → 飞书存储 → 状态反馈
```

### 数据流设计
1. 用户选择报告类型（个人/企业）
2. 上传PDF文件（支持多文件）
3. 后端接收文件并进行基础验证
4. 调用AI API提取数据
5. 验证返回的JSON格式
6. 直接新增记录到飞书表格
7. 返回处理结果给前端

## 📝 开发规范

### 代码风格
- Python: 遵循PEP 8规范
- JavaScript: 使用ES6+语法
- 文件命名: 小写字母+下划线

### 命名约定
- 变量: snake_case
- 函数: snake_case
- 类: PascalCase
- 常量: UPPER_CASE

### Git工作流
- 主分支: master
- 功能分支: feature/功能名
- 提交信息: 中文描述，简洁明确

## 📚 变更记录

### 2024-08-25
- ✅ **项目初始化完成**
  - 创建完整的项目目录结构
  - 初始化Git仓库并完成首次提交
  - 建立Python虚拟环境
  - 安装核心依赖: FastAPI 0.116.1, Uvicorn 0.35.0, Requests 2.32.5等
  - 创建环境变量配置模板
  - 编写项目README文档

- ✅ **后端基础框架完成**
  - 创建FastAPI应用入口 (main.py) - 包含生命周期管理、全局异常处理
  - 实现配置管理系统 (settings.py) - 支持环境变量、文件验证、AI服务商管理
  - 配置CORS中间件 - 支持跨域请求
  - 建立基础API路由结构 - 包含系统信息、文件验证、报告处理端点
  - 实现健康检查端点 - 支持服务状态监控
  - 配置日志系统 - 结构化日志输出
  - 创建开发环境配置文件 (.env)
  - 测试验证: 所有API端点正常响应

- ✅ **PDF处理服务完成**
  - 安装PDF处理库: PyPDF2 3.0.1, pdfplumber 0.10.3
  - 实现PDF文件验证器 - 支持格式检查、大小限制、页数统计
  - 实现双引擎文本提取 - PyPDF2 + pdfplumber，自动选择最佳结果
  - 实现文本提取质量评估 - 包含评分、质量等级、问题诊断
  - 实现批量文件处理 - 支持多文件并发处理、进度跟踪
  - 实现临时文件管理 - 自动清理机制
  - 创建完整数据模型 (schemas.py) - 包含所有处理结果的结构化定义
  - 新增API端点: /api/extract-text - 用于PDF文本提取测试
  - 创建自我测试脚本 - 验证所有核心功能正常工作
  - 测试验证: 文件验证、文本提取、批量处理功能全部通过

### 架构决策记录
- **技术栈选择**: 选择FastAPI而非Flask，因为异步处理能力和自动文档生成
- **AI服务策略**: 采用多服务商支持策略，Gemini为主，Kimi和OpenAI为备选
- **数据存储方案**: 使用飞书多维表格，个人和企业征信数据存储在同一表格的JSON列中

## ⚠️ 风险与约定

### 已知风险
- AI API调用可能失败或超时
- PDF文件格式多样性可能影响解析
- 飞书API限流可能影响批量处理

### 特殊约定
- 每次AI调用都自动加载此上下文文件
- 上下文更新由AI自动完成，但需明确显示更新内容
- 重点关注当前任务状态和技术决策
- 版本管理与代码版本同步

### 注意事项
- 环境变量必须正确配置才能运行
- PDF文件大小限制需要前端验证
- 错误处理需要用户友好的提示信息
