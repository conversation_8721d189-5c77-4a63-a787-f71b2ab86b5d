# 征信报告自动化处理工具 - 开发计划

## 项目概述

### 项目目标
开发一个独立的Web应用工具，实现对个人征信报告和企业征信报告PDF文件的自动化处理，通过AI提取数据并存储到飞书多维表格。

### 核心功能
- 支持个人征信和企业征信两种报告类型
- 批量处理PDF文件（最多10份）
- 使用AI自动提取结构化数据
- 直接新增记录到飞书多维表格
- 实时处理状态展示
- 失败重试机制

## 技术栈

### 后端
- **框架**: Python + FastAPI
- **核心依赖**:
  - `fastapi`: Web框架
  - `python-multipart`: 文件上传处理
  - `google-generativeai`: Gemini API客户端
  - `requests`: 飞书API调用
  - `PyPDF2`: PDF文件处理
  - `python-dotenv`: 环境变量管理
  - `uvicorn`: ASGI服务器

### 前端
- **技术**: 原生HTML/CSS/JavaScript
- **样式**: Tailwind CSS
- **基础**: 基于现有上传页面代码改造

### AI服务
- **主要**: Google Gemini 1.5 Pro
- **备选**: Moonshot Kimi API, OpenAI GPT-4

## 系统架构

### 项目结构
```
credit-report-processor/
├── backend/
│   ├── app/
│   │   ├── main.py              # FastAPI应用入口
│   │   ├── api/
│   │   │   └── routes.py        # API路由
│   │   ├── services/
│   │   │   ├── ai_service.py    # AI API调用服务
│   │   │   ├── feishu_service.py # 飞书API服务
│   │   │   └── pdf_service.py   # PDF处理服务
│   │   ├── config/
│   │   │   ├── settings.py      # 配置管理
│   │   │   └── prompts.py       # AI提示词配置
│   │   ├── models/
│   │   │   └── schemas.py       # 数据模型
│   │   └── utils/
│   │       ├── validators.py    # 数据验证
│   │       └── helpers.py       # 工具函数
│   ├── requirements.txt
│   ├── .env.example
│   └── README.md
├── frontend/
│   ├── index.html              # 主页面
│   ├── assets/
│   │   ├── css/
│   │   │   └── custom.css
│   │   └── js/
│   │       └── app.js
└── docs/
    ├── API文档.md
    └── 部署指南.md
```

### 核心流程
1. 用户选择报告类型（个人/企业）
2. 上传PDF文件（支持多文件）
3. 后端接收文件并进行基础验证
4. 调用AI API提取数据
5. 验证返回的JSON格式
6. 直接新增记录到飞书表格
7. 返回处理结果给前端

### 数据流
```
PDF文件 → AI提取 → JSON验证 → 飞书存储 → 状态反馈
```

## 配置要求

### 环境变量
```
# AI服务配置
GEMINI_API_KEY=your_gemini_key
KIMI_API_KEY=your_kimi_key
OPENAI_API_KEY=your_openai_key

# 飞书配置
FEISHU_APP_ID=cli_xxx
FEISHU_APP_SECRET=xxx
FEISHU_APP_TOKEN=OJHTbcz95ad2q3szuWJcCRJLnCb
FEISHU_TABLE_ID=tbliPO5u586c362N

# 应用配置
MAX_FILE_SIZE=10485760  # 10MB
MAX_BATCH_SIZE=52428800 # 50MB
MAX_FILES_PER_BATCH=10
```

### 文件限制
- 单个PDF文件: 最大10MB
- 批次总大小: 最大50MB
- 批次文件数: 最大10个
- 支持格式: .pdf

### 性能预期
- 单文件处理时间: 30-60秒
- 10文件批次处理: 5-10分钟
- 并发处理: 支持单用户多批次

## 开发阶段

### 第一阶段 - MVP (2-3周)
**目标**: 基础功能实现，单用户使用

**功能范围**:
- 基础文件上传界面
- PDF文件处理
- Gemini API集成
- 飞书API集成
- 基础错误处理
- 实时状态展示

### 第二阶段 - 增强 (1-2周)
**目标**: 功能完善，提升用户体验

**功能范围**:
- 多AI服务商支持
- 失败重试机制
- 处理历史记录
- 批量操作优化
- 详细错误信息

### 第三阶段 - 多用户 (2-3周)
**目标**: 支持多用户访问

**功能范围**:
- 用户认证系统
- 权限管理
- 数据隔离
- 使用统计

## 关键技术要点

### AI服务集成
- 实现多服务商切换机制
- 统一的提示词管理
- 响应格式验证

### 飞书API集成
- Token管理和刷新
- 错误重试机制
- 批量操作优化

### 文件处理
- PDF内容提取
- 文件大小验证
- 临时文件管理

### 错误处理
- 分层错误处理
- 用户友好的错误信息
- 失败重试队列

## 部署方案

### 本地开发
```bash
# 后端启动
cd backend
pip install -r requirements.txt
uvicorn app.main:app --reload --port 8000

# 前端访问
# 直接打开 frontend/index.html
```

### 生产部署
- 使用Docker容器化
- Nginx反向代理
- 环境变量配置
- 日志管理

## 测试策略

### 单元测试
- AI服务调用测试
- 飞书API测试
- PDF处理测试
- 数据验证测试

### 集成测试
- 完整流程测试
- 错误场景测试
- 性能测试

### 用户测试
- 界面易用性测试
- 批量处理测试
- 错误恢复测试
