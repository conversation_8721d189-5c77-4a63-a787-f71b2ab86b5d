  // ==UserScript==
// @name         Feishu Uploader Helper – add-only
// @namespace    http://tampermonkey.net/
// @version      3.1
// @description  Push local JSON to Feishu Bitable (always insert, no dedup)
// <AUTHOR>
// @match        file://*/*
// @connect      open.feishu.cn
// @grant        GM_xmlhttpRequest
// @run-at       document-idle
// ==/UserScript==

(function () {
  'use strict';

  /* ===== 0. 仅在目标页面运行 ===== */
  if (!document.getElementById('upload-btn')) return;
  console.log('Feishu Uploader Helper: Script is active on the uploader page.');

  /* ===== 1. 配置 ===== */
  const FEISHU = {
    APP_ID:     'cli_a8cd1d55e7f7901c',   // ← 自建应用 app_id
    APP_SECRET: 'mzCZdAxwQLVwkt8lOpG3Ib4e1kZ7Tcdl',
    APP_TOKEN:  'OJHTbcz95ad2q3szuWJcCRJLnCb',  // ← **Bitable 的 App Token**（以 app / basc 开头）
    TABLE_ID:   'tbliPO5u586c362N',     // ← 17 位 tbl...
    COMPANY_COL: 'company',                // 列名必须和表里一致
    JSON_COL:    'JSON'                    // 列名必须和表里一致
  };

 /* ===== 2. 获取 tenant_access_token ===== */
  let cachedToken = null, tokenExpire = 0;
  async function getToken () {
    if (cachedToken && Date.now() < tokenExpire) return cachedToken;
    return new Promise((res, rej) => {
      GM_xmlhttpRequest({
        method: 'POST',
        url: 'https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal',
        headers: { 'Content-Type': 'application/json' },
        data: JSON.stringify({ app_id: FEISHU.APP_ID, app_secret: FEISHU.APP_SECRET }),
        anonymous: true,
        onload: r => {
          console.log('⏬ token raw', r.status, r.responseText);
          try {
            const d = JSON.parse(r.responseText);
            if (d.code === 0) {
              cachedToken = d.tenant_access_token;
              tokenExpire = Date.now() + (d.expire - 300) * 1000;
              res(cachedToken);
            } else rej(new Error(`${d.code}: ${d.msg}`));
          } catch (e) { rej(e); }
        },
        onerror: rej
      });
    });
  }

  /* ===== 3. 新增记录 ===== */
  async function createRecord (token, recordData) {
    return new Promise((res, rej) => {
      GM_xmlhttpRequest({
        method: 'POST',
        url: `https://open.feishu.cn/open-apis/bitable/v1/apps/${FEISHU.APP_TOKEN}/tables/${FEISHU.TABLE_ID}/records`,
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json; charset=utf-8'
        },
        data: JSON.stringify(recordData),
        anonymous: true,
        onload: r => {
          console.log('⏬ upsert raw', r.status, r.responseText);
          try {
            const d = JSON.parse(r.responseText);
            if (d.code === 0) res(d.data.record);
            else rej(new Error(`${d.code}: ${d.msg}`));
          } catch (e) { rej(e); }
        },
        onerror: rej
      });
    });
  }

  /* ===== 4. 主流程 ===== */
  async function handleUpload (jsonText) {
    const dispatch = (type, msg) =>
      document.dispatchEvent(new CustomEvent('uploadStatus', { detail: { type, msg } }));

    try {
      dispatch('loading', '正在上传…');
      const dataObj = JSON.parse(jsonText);      // 原始 JSON
      const company  = dataObj.company_name || ''; // 没有也可为空

      const token = await getToken();
      const record = {
        fields: {
          [FEISHU.COMPANY_COL]: company,
          [FEISHU.JSON_COL]: JSON.stringify(dataObj, null, 2)
        }
      };
      await createRecord(token, record);
      dispatch('success', '✅ 新增成功！');
    } catch (e) {
      console.error('❌ 上传失败', e);
      dispatch('error', `❌ 失败: ${e.message}`);
    }
  }

  /* ===== 5. 等待宿主页面触发 ===== */
  document.addEventListener('uploadToFeishu', e => {
    const { jsonText } = e.detail || {};
    if (jsonText) handleUpload(jsonText);
  });
})();