
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gemini to Feishu Uploader</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f8fafc;
        }
        #json-input:empty:before {
            content: attr(placeholder);
            pointer-events: none;
            display: block;
            color: #9ca3af;
        }
        #upload-btn:disabled {
            background-color: #9ca3af;
            cursor: not-allowed;
        }
    </style>
</head>
<body class="flex items-center justify-center min-h-screen">

    <div class="w-full max-w-4xl mx-auto p-6 md:p-8">
        <div class="bg-white rounded-2xl shadow-lg overflow-hidden">
            <div class="p-8 border-b border-gray-200">
                <h1 class="text-2xl font-bold text-gray-800">Gemini to Feishu Uploader</h1>
                <p class="mt-2 text-gray-600">一个用于将Gemini生成的JSON数据推送到飞书多维表格的中转工具。</p>
            </div>

            <div class="p-8">
                <div class="space-y-6">
                    <div>
                        <label for="json-input" class="text-lg font-semibold text-gray-700">1. 粘贴JSON数据</label>
                        <div class="mt-2 rounded-xl bg-gray-100 border border-gray-200 p-1 focus-within:ring-2 focus-within:ring-blue-500">
                            <div id="json-input" class="block w-full p-4 text-sm text-gray-800 min-h-[250px] whitespace-pre-wrap break-words bg-transparent outline-none" contenteditable="true" placeholder="在这里粘贴从Gemini复制的JSON..."></div>
                        </div>
                        <p id="status-text" class="text-sm text-gray-500 mt-2 h-5"></p>
                    </div>
                    <div>
                        <label class="text-lg font-semibold text-gray-700">2. 推送到飞书</label>
                        <button id="upload-btn" class="mt-2 w-full flex items-center justify-center gap-2 rounded-lg bg-blue-600 px-8 py-4 text-lg font-semibold text-white shadow-md transition-all hover:bg-blue-700 disabled:hover:bg-gray-400">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/><polyline points="17 8 12 3 7 8"/><line x1="12" x2="12" y1="3" y2="15"/></svg>
                            <span>推送数据</span>
                        </button>
                    </div>
                </div>
            </div>

            <div class="bg-gray-50 px-8 py-4 text-xs text-gray-500 text-center">
                <p>油猴脚本会自动为“推送数据”按钮赋予上传功能。</p>
            </div>
        </div>
    </div>

    <script>
        // 本地页面的脚本，用于和油猴脚本通信
        const uploadBtn = document.getElementById('upload-btn');
        const jsonInput = document.getElementById('json-input');
        const statusText = document.getElementById('status-text');

        // 默认禁用按钮
        uploadBtn.disabled = true;

        jsonInput.addEventListener('input', () => {
            // 只有当输入框有内容时才启用按钮
            uploadBtn.disabled = jsonInput.innerText.trim() === '';
            statusText.textContent = ''; // 清除状态
        });

        uploadBtn.addEventListener('click', () => {
            // 触发一个自定义事件，油猴脚本会监听这个事件
            const event = new CustomEvent('uploadToFeishu', {
                detail: { jsonText: jsonInput.innerText }
            });
            document.dispatchEvent(event);
        });

        // 监听来自油猴脚本的状态更新
        document.addEventListener('uploadStatus', (e) => {
            const { type, message } = e.detail;
            uploadBtn.disabled = true;
            statusText.textContent = message;
            statusText.style.color = type === 'success' ? '#16a34a' : '#dc2626';
            uploadBtn.querySelector('span').textContent = message;

            if (type !== 'loading') {
                setTimeout(() => {
                    uploadBtn.disabled = jsonInput.innerText.trim() === '';
                    uploadBtn.querySelector('span').textContent = '推送数据';
                    if (type === 'success') {
                        jsonInput.innerText = ''; // 成功后清空
                        uploadBtn.disabled = true;
                    }
                }, 2500);
            }
        });
    </script>

</body>
</html>