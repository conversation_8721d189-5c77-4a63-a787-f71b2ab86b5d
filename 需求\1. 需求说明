1. 项目概述
1.1 项目目标
开发一个独立的Web应用工具，旨在实现对个人征信报告和企业征信报告两类PDF文件的自动化处理。工具需能根据用户指定的报告类型，调用相应的AI模型提取数据，并将最终生成的、结构不同的JSON对象写入飞书多维表格。核心目标是彻底消除手动复制粘贴数据的环节。

1.2 项目背景
当前流程已验证了单一类型（企业征信）报告的自动化链路。此版本将扩展该能力，使其成为一个支持多类型、多模板的通用数据处理平台。

2. 系统架构
本工具采用经典的前后端分离架构，保持不变。

前端 (Frontend): 一个简洁的单页面Web应用，负责文件上传、报告类型选择和状态展示。

后端 (Backend): 一个API服务，负责根据前端指定的报告类型，编排调用第三方API（Gemini和飞书）来完成核心业务逻辑。

3. 功能需求
3.1 用户工作流

用户在浏览器中打开本工具的Web页面。

用户选择要处理的报告类型（例如，通过单选按钮选择“个人征信”或“企业征信”）。

用户通过文件选择框，一次性选择与所选类型相对应的多份PDF报告（最多10份）。

用户点击“上传并处理”按钮。

前端界面实时显示每一份文件的处理状态，例如：“上传中...”、“AI处理中...”、“写入飞书...”、“成功”或“失败”。

流程结束，用户可以切换报告类型并继续上传新的文件。

3.2 前端详细需求

技术栈: HTML, CSS, JavaScript (无需任何复杂框架)。

页面布局:

一个清晰的标题和简要的功能说明。

新增一个报告类型选择器（例如，使用Radio单选按钮），用于让用户指定当前上传批次的报告类型。

一个支持多文件选择的文件上传区域（可拖拽上传为佳）。

一个明确的“上传并处理”按钮，在没有选择文件时应处于禁用状态。

一个动态的状态展示列表，用于实时反馈每个文件的处理进度和结果。

核心功能:

文件校验: 在前端进行基础校验，只允许用户选择.pdf后缀的文件。

API通信: 点击按钮后，将所有选中的文件以及用户选择的报告类型通过POST请求（使用multipart/form-data格式）发送到后端API的指定端点。

状态展示: 能够清晰地展示每个文件的实时状态，成功或失败的结果需要有明确的视觉区分（如绿色/红色图标）。

3.3 后端详细需求

技术栈: 推荐使用轻量级框架，如 Python (Flask/FastAPI) 或 Node.js (Express)。

API端点:

提供一个POST端点，例如 /api/process-reports，用于接收前端上传的PDF文件数组和报告类型标识（如 report_type: 'personal' 或 report_type: 'corporate'）。

核心处理逻辑 (针对每个上传的PDF文件):

接收文件和报告类型。

调用Gemini API:

根据前端传递的report_type，从后端配置中选择对应的提示词（见附录A.1或A.2）。

使用官方的Gemini API（推荐Gemini 1.5 Pro），将PDF文件内容和选定的提示词一同发送。

必须处理API可能返回的错误。

解析与校验:

接收Gemini API返回的JSON数据，并校验其合法性。

调用飞书多维表格API:

根据报告类型，确定用于查询/更新的唯一标识符（例如，企业报告使用company_name，个人报告使用report_number）。

调用飞书API的查询接口，判断记录是否已存在。

根据查询结果，调用更新或新增接口，将完整的JSON对象写入指定的单元格。

错误处理: 整个流程需要有完善的错误处理机制，并向前台返回明确的失败状态和原因。

4. 非功能性需求
(本节内容保持不变)

4.1 安全性

所有API密钥必须作为环境变量存储在后端。

后端服务器的公网IP地址必须被添加到飞书应用的“IP白名单”中。

4.2 配置

飞书的APP_TOKEN、TABLE_ID等配置信息应作为环境变量或配置文件。