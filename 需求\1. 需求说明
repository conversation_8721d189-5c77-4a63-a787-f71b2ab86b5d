orate'）。

核心处理逻辑 (针对每个上传的PDF文件):

接收文件和报告类型。

调用Gemini API:

根据前端传递的report_type，从后端配置中选择对应的提示词（见附录A.1或A.2）。

使用官方的Gemini API（推荐Gemini 1.5 Pro），将PDF文件内容和选定的提示词一同发送。

必须处理API可能返回的错误。

解析与校验:

接收Gemini API返回的JSON数据，并校验其合法性。

调用飞书多维表格API:

根据报告类型，确定用于查询/更新的唯一标识符（例如，企业报告使用company_name，个人报告使用report_number）。

调用飞书API的查询接口，判断记录是否已存在。

根据查询结果，调用更新或新增接口，将完整的JSON对象写入指定的单元格。

错误处理: 整个流程需要有完善的错误处理机制，并向前台返回明确的失败状态和原因。

4. 非功能性需求
(本节内容保持不变)

4.1 安全性

所有API密钥必须作为环境变量存储在后端。

后端服务器的公网IP地址必须被添加到飞书应用的“IP白名单”中。

4.2 配置

飞书的APP_TOKEN、TABLE_ID等配置信息应作为环境变量或配置文件。

具体的提示词和油猴脚本已经有初版，参考对应的文档。